<?php
require_once __DIR__ . '/../../printout.helper.php';

class C_Pruefprotokoll
{
    public function getData($schemaId, $documentId): array
    {
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());
        $doc = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $curl);
        $data = PrintoutHelper::mapDocumentChildrenToValues($doc['fullDocument'], injectParentTitle: true);
        $data['company'] = PrintoutHelper::downloadInfoSettings($curl);
        $partial = "customerDisplayName,projectSiteAddress,projectSiteZipCode,projectSiteCity";
        $data['project'] = PrintoutHelper::downloadProject($doc['fullDocument']['documentRelKey1'], $partial, $curl);
        $data['logo'] = PrintoutHelper::downloadSettings($curl)['logo'];
        return $data;
    }
}