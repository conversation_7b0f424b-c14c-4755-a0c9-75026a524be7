<?php
namespace Pruefprotokoll;

use C_Pruefprotokoll;
use PrintoutHelper;

/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_Pruefprotokoll())->getData($_GET['schemaId'], $_GET['documentId']);
}

function getDataField($data, $field, $default = '')
{
    return $data[$field] ?? $default;
}

function renderCheckbox($data, $key, $value): void
{
    echo '<span class="checkbox-size">';
    echo ($data[$key] ?? '') === $value ? '&#9745;' : '&#9744;';
    echo '</span>';
}

function renderMultiCheckbox($data, $key, $values): void
{
    foreach ($values as $value) {
        echo '<span class="checkbox-size">';
        echo (in_array($value, $data[$key] ?? [])) ? '&#9745;' : '&#9744;';
        echo " $value</span>";
    }
}

?>

<html lang="de">
<head>
    <title>Prüfprotokoll für Arbeits- und Schutzgerüste</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta charset="UTF-8">
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
</head>
<body>
<div class="landscape">
    <div class="table-left">
        <table class="standard-font" style="border: 2px solid #cdcdcd; border-collapse: collapse; width: 100%">
            <tr>
                <td colspan="2"
                    style="font-size: 140%; font-weight: bold; border-bottom: 2px solid #cdcdcd; padding-left: 10px">
                    Prüfprotokoll für Arbeits- und Schutzgerüste
                </td>
            </tr>
            <tr>
                <td style="width: 35%; vertical-align: top; padding: 10px">
                    <b>Gerüstersteller</b> <span style="font-size: 80%">(ggf. Stempel)</span>
                    <?php
                    if (!empty($data['company']['companyName'])) {
                        echo '<br>' . $data['company']['companyName'];
                    }
                    if (!empty($data['company']['contactInfo'])) {
                        echo '<br>' . $data['company']['contactInfo'];
                    }
                    $formattedAddress = PrintoutHelper::formatAddress(
                        $data['company']['address']['address'] ?? null,
                        $data['company']['address']['postcode'] ?? null,
                        $data['company']['address']['city'] ?? null
                    );
                    if ($formattedAddress !== "") {
                        echo '<br>' . $formattedAddress;
                    }
                    ?>
                </td>
                <td style="width: 65%; padding: 10px">
                    <table class="standard-font" style="width: 100%; border-collapse: collapse">
                        <tr>
                            <td style="padding: 0">
                                <b>Baustelle:</b>
                                <?php
                                $formattedAddress = PrintoutHelper::formatAddress(
                                    $data['project']['projectSiteAddress'] ?? null,
                                    $data['project']['projectSiteZipCode'] ?? null,
                                    $data['project']['projectSiteCity'] ?? null
                                );
                                if ($formattedAddress !== "") {
                                    echo $formattedAddress;
                                }
                                ?>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 5px 0">
                                <b>Auftraggeber:</b>
                                <?= $data['project']['customerDisplayName'] ?? '' ?>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 0 0 5px;">
                                <b>Befähigte Person:</b>
                                <?= nl2br(getDataField($data, 'Gerüstersteller (ggf. Stempel) Befähigte Person')) ?>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        <table class="standard-font"
               style="border: 2px solid #cdcdcd; border-collapse: collapse; border-top: 0; width: 100%">
            <tr>
                <td colspan="4"><b>Arbeitsgerüst</b> (DIN EN 12811) als</td>
            </tr>
            <tr>
                <td class="border-bottom">
                    <span>
                        <?php renderMultiCheckbox($data, 'Arbeitsgerüst (DIN EN 12811) Arbeitsgerüst als', ['Fassadengerüst']); ?>
                    </span>
                </td>
                <td class="border-bottom">
                    <span>
                        <?php renderMultiCheckbox($data, 'Arbeitsgerüst (DIN EN 12811) Arbeitsgerüst als', ['Raumgerüst']); ?>
                    </span>
                </td>
                <td class="border-bottom">
                    <span>
                        <?php renderMultiCheckbox($data, 'Arbeitsgerüst (DIN EN 12811) Arbeitsgerüst als', ['Fahrgerüst']); ?>
                    </span>
                </td>
                <td class="border-bottom">
                    <span>
                    </span>
                </td>
            </tr>
            <tr>
                <td colspan="4"><b>Schutzgerüst</b> (DIN 4420) als</td>
            </tr>
            <tr>
                <td style="width: 25%">
                    <?php renderMultiCheckbox($data, 'Schutzgerüst (DIN 4420) Schutzgerüst als', ['Fanggerüst']); ?>
                </td>
                <td style="width: 25%">
                    <?php renderMultiCheckbox($data, 'Schutzgerüst (DIN 4420) Schutzgerüst als', ['Dachfanggerüst']); ?>
                </td>
                <td style="width: 25%">
                    <?php renderMultiCheckbox($data, 'Schutzgerüst (DIN 4420) Schutzgerüst als', ['Schutzdach']); ?>
                </td>
                <td style="width: 25%">
                    <?php renderMultiCheckbox($data, 'Schutzgerüst (DIN 4420) Schutzgerüst als', ['Treppenturm']); ?>
                </td>
            </tr>
            <tr>
                <td colspan="4" class="border-bottom"><b>Sondergerüste:
                        <u><?= nl2br(getDataField($data, 'Schutzgerüst (DIN 4420) Sondergerüste')) ?></u></b>
                </td>
            </tr>
            <tr>
                <td colspan="4"><b>Lastklasse</b></td>
            </tr>
            <tr>
                <td>
                    <?php renderMultiCheckbox($data, 'Lastklasse Lastklasse', ['2 (1.5 kN/m²)']); ?>
                </td>
                <td>
                    <?php renderMultiCheckbox($data, 'Lastklasse Lastklasse', ['3 (2,0 kN/m²)']); ?>
                </td>
                <td>
                    <?php renderMultiCheckbox($data, 'Lastklasse Lastklasse', ['4 (3,0 kN/m²)']); ?>
                </td>
                <td>
                    <?php renderMultiCheckbox($data, 'Lastklasse Lastklasse', ['(  kN/m²)']); ?>
                </td>
            </tr>
            <tr>
                <td colspan="4" class="border-bottom">Die Summe der Verkehrslasten aller übereinanderliegenden
                    Gerüstlagen
                    in einem Gerüstfeld <br>
                    darf den vorgenannten Wert nicht überschreiten.
                </td>
            </tr>
            <tr>
                <td><b>Breitenklasse</b></td>
                <td>
                    <?php renderMultiCheckbox($data, 'Breitenklasse Breitenklasse', ['W06']); ?>
                </td>
                <td>
                    <?php renderMultiCheckbox($data, 'Breitenklasse Breitenklasse', ['W09']); ?>
                </td>
                <td class="checkbox-size">&#9744</td>
            </tr>
            <tr>
                <td colspan="4" class="border-bottom">Nutzungsbeschränkung:
                    <b><u><?= nl2br(getDataField($data, 'Breitenklasse Nutzungsbeschränkung')) ?></u></b>
                </td>
            </tr>
        </table>
        <table class="standard-font"
               style="width: 50%; border: 2px solid #cdcdcd; border-collapse: collapse; border-top: 0; border-right: 0; float: left">
            <tr>
                <td colspan="2" style="font-size: 115%; padding-bottom: 10px"><b>Durch befähigte Person des <br>
                        Gerüststellers
                        geprüft</b></td>
            </tr>
            <tr>
                <td style="width: 20%">
                    <?= isset($data['Durch befähigte Person des Gerüsterstellers geprüft Datum'])
                        ? date('d.m.Y', strtotime($data['Durch befähigte Person des Gerüsterstellers geprüft Datum']))
                        : ''; ?>
                </td>
                <td style="width: 80%; display: flex; align-items: center"><?= nl2br(getDataField($data, 'Durch befähigte Person des Gerüsterstellers geprüft Name')) ?>

                    <?php if (isset($data['Durch befähigte Person des Gerüsterstellers geprüft Unterschrift']) && $data['Durch befähigte Person des Gerüsterstellers geprüft Unterschrift']) { ?>
                        <img src="<?= $data['Durch befähigte Person des Gerüsterstellers geprüft Unterschrift'] ?>"
                             alt="signature1" class="signatures">
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td style="font-size: 75%; border-top: 1px solid black">Datum</td>
                <td style="font-size: 75%; border-top: 1px solid black">Name/Unterschrift</td>
            </tr>
        </table>
        <table class="standard-font"
               style="width: 50%; border: 2px solid #cdcdcd; border-collapse: collapse; border-top: 0">
            <tr>
                <td colspan="2" style="font-size: 115%; padding-bottom: 10px"><b>Der Auftraggeber* <br> <span
                                style="color: white">X</span></b>
                </td>
            </tr>
            <tr>
                <td style="width: 20%">
                    <?= isset($data['Der Auftraggeber Datum'])
                        ? date('d.m.Y', strtotime($data['Der Auftraggeber Datum']))
                        : ''; ?>
                </td>
                <td style="width: 80%; display: flex; align-items: center"><?= nl2br(getDataField($data, 'Der Auftraggeber Name')) ?>
                    <?php if (isset($data['Der Auftraggeber Unterschrift']) && $data['Der Auftraggeber Unterschrift']) { ?>
                        <img src="<?= $data['Der Auftraggeber Unterschrift'] ?>" alt="signature1" class="signatures">
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td style="font-size: 75%; border-top: 1px solid black">Datum</td>
                <td style="font-size: 75%; border-top: 1px solid black">Name/Unterschrift</td>
            </tr>
        </table>

        <table class="standard-font"
               style="width: 100%; border: 2px solid #cdcdcd; border-collapse: collapse; border-top: 0">
            <tr>
                <td colspan="5"><b>Warnhinweise:</b></td>
            </tr>
            <tr>
                <td>
                    <img src="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/img/blue.png") ?>" alt=""
                         style="width: 100%;height: 100%; object-fit: contain;">
                </td>
                <td>
                    <img src="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/img/0.png") ?>"
                         alt="" class="image-style">
                </td>
                <td>
                    <img src="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/img/6.png") ?>"
                         alt="" class="image-style">
                </td>
                <td>
                    <img src="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/img/9.png") ?>"
                         alt="" class="image-style">
                </td>
            </tr>
            <tr>
                <td>
                    <img src="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/img/5.png") ?>"
                         alt="" class="image-style">
                </td>
                <td>
                    <img src="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/img/7.png") ?>"
                         alt="" class="image-style">
                </td>
                <td>
                    <img src="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/img/4.png") ?>"
                         alt="" class="image-style">
                </td>
                <td>
                    <img src="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/img/30cm.png") ?>"
                         alt="" class="image-style">
                </td>
            </tr>
            <tr>
                <td>
                    <img src="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/img/1.png") ?>"
                         alt="" class="image-style">
                </td>
                <td>
                    <img src="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/img/elevator.png") ?>"
                         alt="" class="image-style">
                </td>
                <td>
                    <img src="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/img/8.jpg") ?>"
                         alt="" class="image-style">
                </td>
                <td>
                    <img src="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/img/10.png") ?>" alt=""
                         class="image-style">
                </td>
            </tr>
        </table>
    </div>
    <div class="table-right">

        <table class="standard-font"
               style="  border-collapse: collapse; width: 100%">
            <tr>
                <td colspan="5"
                    style="font-size: 140%; font-weight: bold; border-right: 2px solid #cdcdcd; border-bottom: 2px solid #cdcdcd; border-top: 2px solid #cdcdcd; border-left: 2px solid #cdcdcd;  padding-left: 15px">
                    CHECKLISTE
                </td>
            </tr>
            <tr>
                <td style="width: 20%; border-left: 2px solid #cdcdcd; border-right: 2px solid #cdcdcd">
                </td>
                <td style="width: 50%; border-left: 2px solid #cdcdcd; border-right: 2px solid #cdcdcd">
                    <b>Überprüfung</b>
                </td>
                <td colspan="2"
                    style="width: 15%; text-align: center; border-left: 2px solid #cdcdcd; border-right: 2px solid #cdcdcd ">
                    <b>in Ordnung</b>
                </td>
                <td style="width: 15%; text-align: center; border-left: 2px solid #cdcdcd; border-right: 2px solid #cdcdcd; border-top: 2px solid #cdcdcd">
                    <b>nicht</b>
                </td>
            </tr>
            <tr>
                <td style="width: 20%; border-left: 2px solid #cdcdcd; border-right: 2px solid #cdcdcd"></td>
                <td style="width: 50%; border-left: 2px solid #cdcdcd; border-right: 2px solid #cdcdcd"></td>
                <td style="width: 7.5%; text-align: center; border-left: 2px solid #cdcdcd ">
                    <b>ja</b>
                </td>
                <td style="width: 7.5%; text-align: center; border-right: 2px solid #cdcdcd ">
                    <b>nein</b>
                </td>
                <td style="width: 15%; text-align: center; border-left: 2px solid #cdcdcd; border-right: 2px solid #cdcdcd">
                    <b>zutreffend</b>
                </td>
                <td class="rotate" rowspan="100"
                    style="vertical-align: bottom; font-size: 85%; writing-mode: vertical-lr; ">Verteiler: Baustelle,
                    Auftraggeber, Gerüstersteller
                </td>
            </tr>
            <tr>
                <td style="border: 2px solid #cdcdcd; padding-left:15px"><b>Gerüstbauteile</b></td>
                <td style="border: 2px solid #cdcdcd">augenscheinlich unbeschädigt</td>
                <td class="checkbox-style">
                    <?php renderCheckbox($data, 'Gerüstbauteile Augenscheinlich unbeschädigt', 'ja'); ?>
                </td>
                <td class="checkbox-style">
                    <?php renderCheckbox($data, 'Gerüstbauteile Augenscheinlich unbeschädigt', 'nein'); ?>
                </td>
                <td class="checkbox-style">
                    <?php renderCheckbox($data, 'Gerüstbauteile Augenscheinlich unbeschädigt', 'nicht zutreffend'); ?>
                </td>
            </tr>
            <tr>
                <td rowspan="6" style="border: 2px solid #cdcdcd; padding-left:15px; vertical-align: top"><b>Standsicherheit</b>
                </td>
                <td style="border: 2px solid #cdcdcd">Tragfähigkeit der Aufstandsfläche</td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Standsicherheit Tragfähigkeit der Aufstandsfläche', 'ja'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Standsicherheit Tragfähigkeit der Aufstandsfläche', 'nein'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Standsicherheit Tragfähigkeit der Aufstandsfläche', 'nicht zutreffend'); ?></td>
            </tr>
            <tr>
                <td style="border: 2px solid #cdcdcd">Fußspindel – Auszugslänge</td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Standsicherheit Fußspindel – Auszugslänge', 'ja'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Standsicherheit Fußspindel – Auszugslänge', 'nein'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Standsicherheit Fußspindel – Auszugslänge', 'nicht zutreffend'); ?></td>
            </tr>
            <tr>
                <td style="border: 2px solid #cdcdcd">Verstrebungen / Diagonalen</td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Standsicherheit Verstrebungen / Diagonalen', 'ja'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Standsicherheit Verstrebungen / Diagonalen', 'nein'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Standsicherheit Verstrebungen / Diagonalen', 'nicht zutreffend'); ?></td>
            </tr>
            <tr>
                <td style="border: 2px solid #cdcdcd">Längsriegel – in Fußpunkthöhe</td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Standsicherheit Längsriegel – in Fußpunkthöhe', 'ja'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Standsicherheit Längsriegel – in Fußpunkthöhe', 'nein'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Standsicherheit Längsriegel – in Fußpunkthöhe', 'nicht zutreffend'); ?></td>
            </tr>
            <tr>
                <td style="border: 2px solid #cdcdcd">Gitterträger – Aussteifungen</td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Standsicherheit Gitterträger – Aussteifungen', 'ja'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Standsicherheit Gitterträger – Aussteifungen', 'nein'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Standsicherheit Gitterträger – Aussteifungen', 'nicht zutreffend'); ?></td>
            </tr>
            <tr>
                <td style="border: 2px solid #cdcdcd">Verankerungen – nach Montageanweisung/ <br>
                    Aufbau- und Verwendungsanleitung
                </td>
                <td style="border: 2px solid #cdcdcd; text-align: center; vertical-align: bottom"><?php renderCheckbox($data, 'Standsicherheit Verankerungen – nach Montageanweisung/Aufbau- und Verwendungsanleitung', 'ja'); ?></td>
                <td style="border: 2px solid #cdcdcd; text-align: center; vertical-align: bottom"><?php renderCheckbox($data, 'Standsicherheit Verankerungen – nach Montageanweisung/Aufbau- und Verwendungsanleitung', 'nein'); ?></td>
                <td style="border: 2px solid #cdcdcd; text-align: center; vertical-align: bottom"><?php renderCheckbox($data, 'Standsicherheit Verankerungen – nach Montageanweisung/Aufbau- und Verwendungsanleitung', 'nicht zutreffend'); ?></td>
            </tr>
            <tr>
                <td rowspan="5" style="border: 2px solid #cdcdcd; padding-left:15px; vertical-align: top"><b>Beläge</b>
                </td>
                <td style="border: 2px solid #cdcdcd">Gerüstlagen – voll ausgelegt /Belagsicherung</td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Beläge Gerüstlagen – voll ausgelegt / Belagssicherung', 'ja'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Beläge Gerüstlagen – voll ausgelegt / Belagssicherung', 'nein'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Beläge Gerüstlagen – voll ausgelegt / Belagssicherung', 'nicht zutreffend'); ?></td>
            </tr>
            <tr>
                <td style="border: 2px solid #cdcdcd">Systembeläge – einschließlich Konsolenbelag</td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Beläge Systembeläge – einschließlich Konsolenbelag', 'ja'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Beläge Systembeläge – einschließlich Konsolenbelag', 'nein'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Beläge Systembeläge – einschließlich Konsolenbelag', 'nicht zutreffend'); ?></td>
            </tr>
            <tr>
                <td style="border: 2px solid #cdcdcd">Eckausbildung – in voller Breite herumgeführt</td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Beläge Eckausbildung – in voller Breite herumgeführt', 'ja'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Beläge Eckausbildung – in voller Breite herumgeführt', 'nein'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Beläge Eckausbildung – in voller Breite herumgeführt', 'nicht zutreffend'); ?></td>
            </tr>
            <tr>
                <td style="border: 2px solid #cdcdcd">Gerüstbohlen – Querschnitt, Auflagerung</td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Beläge Gerüsthöhen – Querstabilität, Auflagerung', 'ja'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Beläge Gerüsthöhen – Querstabilität, Auflagerung', 'nein'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Beläge Gerüsthöhen – Querstabilität, Auflagerung', 'nicht zutreffend'); ?></td>
            </tr>
            <tr>
                <td style="border: 2px solid #cdcdcd">Öffnungen – zwischen den Belägen</td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Beläge Öffnungen – zwischen den Belägen', 'ja'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Beläge Öffnungen – zwischen den Belägen', 'nein'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Beläge Öffnungen – zwischen den Belägen', 'nicht zutreffend'); ?></td>
            </tr>
            <tr>
                <td rowspan="9" style="border: 2px solid #cdcdcd; padding-left:15px; vertical-align: top"><b>Arbeits-
                        und
                        <br> Betriebs- <br> sicherheit</b></td>
                <td style="border: 2px solid #cdcdcd">Seitenschutz – einschließlich Stirnseitenschutz</td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Arbeits- und Betriebssicherheit Seitenschutz – einschließlich Stirnseitenschutz', 'ja'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Arbeits- und Betriebssicherheit Seitenschutz – einschließlich Stirnseitenschutz', 'nein'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Arbeits- und Betriebssicherheit Seitenschutz – einschließlich Stirnseitenschutz', 'nicht zutreffend'); ?></td>
            </tr>
            <tr>
                <td style="border: 2px solid #cdcdcd">Wandabstand ≤ 30 cm</td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Arbeits- und Betriebssicherheit Wandabstand ≤ 30 cm', 'ja'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Arbeits- und Betriebssicherheit Wandabstand ≤ 30 cm', 'nein'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Arbeits- und Betriebssicherheit Wandabstand ≤ 30 cm', 'nicht zutreffend'); ?></td>
            </tr>
            <tr>
                <td style="border: 2px solid #cdcdcd">innenliegender Seitenschutz</td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Arbeits- und Betriebssicherheit Innengeländer Seitenschutz', 'ja'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Arbeits- und Betriebssicherheit Innengeländer Seitenschutz', 'nein'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Arbeits- und Betriebssicherheit Innengeländer Seitenschutz', 'nicht zutreffend'); ?></td>
            </tr>
            <tr>
                <td style="border: 2px solid #cdcdcd">Aufstiege, Zugänge – Abstand ≤ 50 m</td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Arbeits- und Betriebssicherheit Aufstiege, Zugänge – Abstand ≤ 50 m', 'ja'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Arbeits- und Betriebssicherheit Aufstiege, Zugänge – Abstand ≤ 50 m', 'nein'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Arbeits- und Betriebssicherheit Aufstiege, Zugänge – Abstand ≤ 50 m', 'nicht zutreffend'); ?></td>
            </tr>
            <tr>
                <td style="border: 2px solid #cdcdcd">Treppenturm, Gerüsttreppe</td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Arbeits- und Betriebssicherheit Treppenturm, Gerüsttreppe', 'ja'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Arbeits- und Betriebssicherheit Treppenturm, Gerüsttreppe', 'nein'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Arbeits- und Betriebssicherheit Treppenturm, Gerüsttreppe', 'nicht zutreffend'); ?></td>
            </tr>
            <tr>
                <td style="border: 2px solid #cdcdcd">Anlegeleiter ≤ 5 m, Leitergang</td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Arbeits- und Betriebssicherheit Anlegeleiter ≤ 5 m, Leitergang', 'ja'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Arbeits- und Betriebssicherheit Anlegeleiter ≤ 5 m, Leitergang', 'nein'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Arbeits- und Betriebssicherheit Anlegeleiter ≤ 5 m, Leitergang', 'nicht zutreffend'); ?></td>
            </tr>
            <tr>
                <td style="border: 2px solid #cdcdcd">Schutzwand</td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Arbeits- und Betriebssicherheit Schutzwand', 'ja'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Arbeits- und Betriebssicherheit Schutzwand', 'nein'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Arbeits- und Betriebssicherheit Schutzwand', 'nicht zutreffend'); ?></td>
            </tr>
            <tr>
                <td style="border: 2px solid #cdcdcd">Schutzdach</td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Arbeits- und Betriebssicherheit Schutzdach', 'ja'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Arbeits- und Betriebssicherheit Schutzdach', 'nein'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Arbeits- und Betriebssicherheit Schutzdach', 'nicht zutreffend'); ?></td>
            </tr>
            <tr>
                <td style="border: 2px solid #cdcdcd">Verkehrssicherung – Beleuchtung</td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Arbeits- und Betriebssicherheit Verkehrssicherung – Beleuchtung', 'ja'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Arbeits- und Betriebssicherheit Verkehrssicherung – Beleuchtung', 'nein'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Arbeits- und Betriebssicherheit Verkehrssicherung – Beleuchtung', 'nicht zutreffend'); ?></td>
            </tr>
            <tr>
                <td rowspan="2" style="border: 2px solid #cdcdcd; padding-left:15px; vertical-align: top">
                    <b>Fahrgerüste</b></td>
                <td style="border: 2px solid #cdcdcd">Fahrrollen</td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Fahrgerüste Fahrrollen', 'ja'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Fahrgerüste Fahrrollen', 'nein'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Fahrgerüste Fahrrollen', 'nicht zutreffend'); ?></td>
            </tr>
            <tr>
                <td style="border: 2px solid #cdcdcd">Ballast / Verbreiterungen</td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Fahrgerüste Ballast / Verbreiterungen', 'ja'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Fahrgerüste Ballast / Verbreiterungen', 'nein'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Fahrgerüste Ballast / Verbreiterungen', 'nicht zutreffend'); ?></td>
            </tr>
            <tr>
                <td style="border: 2px solid #cdcdcd; padding-left:15px; vertical-align: top"><b>Kennzeichnung</b></td>
                <td style="border: 2px solid #cdcdcd">Gerüstkennzeichnung – an den Zugängen
                </td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Kennzeichnung Gerüstkennzeichnung – an den Zugängen', 'ja'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Kennzeichnung Gerüstkennzeichnung – an den Zugängen', 'nein'); ?></td>
                <td class="checkbox-style"><?php renderCheckbox($data, 'Kennzeichnung Gerüstkennzeichnung – an den Zugängen', 'nicht zutreffend'); ?></td>
            </tr>
            <tr>
                <td style="border: 2px solid #cdcdcd; padding-left:15px; vertical-align: top"><b>Sperrung:</b></td>
                <td style="border: 2px solid #cdcdcd">Nicht fertig gestellte Bereiche abgegrenzt <u>und</u> <br>
                    Verbotszeichen „Zutritt verboten“ angebracht
                <td style="border: 2px solid #cdcdcd; text-align: center; vertical-align: bottom"><?php renderCheckbox($data, "Sperrung Nicht fertig gestellte Bereiche abgesperrt und Verbotszeichen 'Zutritt verboten' angebracht", 'ja'); ?></td>
                <td style="border: 2px solid #cdcdcd; text-align: center; vertical-align: bottom"><?php renderCheckbox($data, "Sperrung Nicht fertig gestellte Bereiche abgesperrt und Verbotszeichen 'Zutritt verboten' angebracht", 'nein'); ?></td>
                <td style="border: 2px solid #cdcdcd; text-align: center; vertical-align: bottom"><?php renderCheckbox($data, "Sperrung Nicht fertig gestellte Bereiche abgesperrt und Verbotszeichen 'Zutritt verboten' angebracht", 'nicht zutreffend'); ?></td>
            </tr>
            <tr>
                <td style="border-bottom: 2px solid #cdcdcd; padding-left:15px; vertical-align: top; border-left: 2px solid #cdcdcd">
                    <b>Bemerkungen/
                        Hinweise:</b></td>
                <td colspan=4 style="border-bottom: 2px solid #cdcdcd; border-right: 2px solid #cdcdcd">
                    <?= nl2br(getDataField($data, 'Checkliste Bemerkungen / Hinweise')) ?>
                </td>
            <tr>
            <tr>
                <td colspan="5"
                    style="padding-left: 15px; border-left: 2px solid #cdcdcd; border-bottom: 2px solid #cdcdcd; border-right: 2px solid #cdcdcd">
                    <b>Kennzeichnung am Gerüst nur anbringen, wenn keine Mängel vorhanden sind.</b>
                    <div style="height: 0.5mm"></div>
                    <span style="font-size: 75%;">*Gilt auch ohne Unterschrift des Auftraggebers als Prüfprotokoll</span>
                </td>
            </tr>
        </table>
    </div>
</div>
</body>
</html>